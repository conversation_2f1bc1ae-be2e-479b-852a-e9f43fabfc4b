# ESP32-S3 Pin Configuration Fix

## Problem
The original pin configuration was causing boot issues on ESP32-S3 due to conflicts with strapping pins and pins with special boot behavior.

## Original Problematic Configuration
```
I2C Bus 0 (Sensor 1):
- SDA: GPIO20
- SCL: GPIO47 ❌ (slow reset transition)
- INT1: GPIO45 ❌ (strapping pin for VDD_SPI)

I2C Bus 1 (Sensor 2):
- SDA: GPIO42 ❌ (slow reset transition)
- SCL: GPIO40
- INT1: GPIO38
```

## New Safe Configuration
```
I2C Bus 0 (Sensor 1):
- SDA: GPIO21 ✅ (standard I2C SDA pin)
- SCL: GPIO22 ✅ (standard I2C SCL pin)
- INT1: GPIO4  ✅ (safe GPIO pin)

I2C Bus 1 (Sensor 2):
- SDA: GPIO8  ✅ (safe GPIO pin)
- SCL: GPIO9  ✅ (safe GPIO pin)
- INT1: GPIO5 ✅ (safe GPIO pin)
```

## ESP32-S3 GPIO Limitations to Avoid

### Strapping Pins (cause boot issues):
- **GPIO0**: Boot mode strapping pin
- **GPIO3**: JTAG signal source strapping pin
- **GPIO45**: VDD_SPI voltage strapping pin ❌ (was used for SENSOR1_INT1)
- **GPIO46**: ROM message printing strapping pin

### Pins with Reset Issues:
- **GPIO42**: Transitions low on reset slower than other GPIOs ❌ (was used for I2C_BUS_1_SDA)
- **GPIO47**: Transitions low on reset slower than other GPIOs ❌ (was used for I2C_BUS_0_SCL)
- **GPIO21**: Transitions low on reset slower than other GPIOs (but commonly used for I2C)

### Reserved/Unavailable Pins:
- **GPIO26-32**: Connected to in-package flash/PSRAM (crashes firmware)
- **GPIO33-37**: Unavailable when using octal flash/PSRAM

### Special Behavior:
- **GPIO39**: Goes HIGH after reset (instead of LOW like most pins)

## Wiring Changes Required

You'll need to rewire your sensors to use the new pin assignments:

### Sensor 1 (ADXL345):
- **SDA**: Move from GPIO20 to **GPIO21**
- **SCL**: Move from GPIO47 to **GPIO22**
- **INT1**: Move from GPIO45 to **GPIO4**

### Sensor 2 (ADXL345):
- **SDA**: Move from GPIO42 to **GPIO8**
- **SCL**: Move from GPIO40 to **GPIO9**
- **INT1**: Keep GPIO38 or move to **GPIO5** (both are safe)

## Benefits of New Configuration

1. **No boot conflicts**: Avoids all strapping pins
2. **Reliable I2C**: Uses standard I2C pins and avoids slow-reset pins
3. **Standard practice**: GPIO21/22 are the default I2C pins for ESP32 family
4. **Future-proof**: All selected pins are safe for general GPIO use

## Files Modified
- `firmware/main/firmware.c`: Updated pin definitions
- `firmware/main/interface.h`: Updated pin comments
