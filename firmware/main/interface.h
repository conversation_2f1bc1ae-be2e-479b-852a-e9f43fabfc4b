#include <stdint.h>

#define BURST_SAMPLES 32

#define UART_BAUD_RATE 115200
#define SAMPLES_PER_SECOND 800

// GPIO pins for ADXL345 interrupt lines (INT1) - defined in firmware.c
// #define SENSOR1_INT1_PIN 45  // GPIO45 for sensor 1 interrupt
// #define SENSOR2_INT1_PIN 38  // GPIO38 for sensor 2 interrupt

// Binary packet structure for efficient UART transmission
typedef struct __attribute__((packed))
{
    uint8_t header[4];               // "ADXL" magic header
    uint8_t sensor_id;               // Sensor ID (1 or 2)
    uint8_t sample_count;            // Number of samples
    int16_t accel[BURST_SAMPLES][3]; // Raw accelerometer data
    uint16_t checksum;               // Simple checksum for data integrity
} uart_packet_t;
